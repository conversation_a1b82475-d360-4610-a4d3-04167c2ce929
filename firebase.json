{"hosting": [{"target": "web", "public": "clients/web/dist/client", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "function": "ssr"}]}, {"target": "portal", "public": "clients/portal/dist", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}, {"target": "admin", "public": "clients/fadhili/dist", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}], "functions": {"source": "functions", "predeploy": "npm run build:web && npm run build:functions"}}