import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Shield, Users, Vote, Heart, Handshake, TrendingUp } from "lucide-react";
import { motion } from "framer-motion";

export default function ValueProposition() {
  const features = [
    {
      icon: Heart,
      title: "Built for Trust",
      description: "Everything from proposals to finances is transparent. No hidden books. No drama.",
      gradient: "from-red-500 to-pink-500",
      image: "https://qtrypzzcjebvfcihiynt.supabase.co/storage/v1/object/public/base44-prod/public/10a84215a_happy.jpg",
      imageAlt: "Happy community members"
    },
    {
      icon: Vote,
      title: "Voice to All", 
      description: "Governance is in your hands. Propose, vote, and lead — all from your phone.",
      gradient: "from-purple-500 to-indigo-500",
      image: "https://qtrypzzcjebvfcihiynt.supabase.co/storage/v1/object/public/base44-prod/public/742a47b2e_hands.jpg",
      imageAlt: "Hands coming together in unity"
    },
    {
      icon: TrendingUp,
      title: "Collective Growth",
      description: "Watch your community thrive together. Shared prosperity through transparent collaboration.",
      gradient: "from-green-500 to-emerald-500",
      image: "https://qtrypzzcjebvfcihiynt.supabase.co/storage/v1/object/public/base44-prod/public/ec0585d80_growth.jpg",
      imageAlt: "Community growth and development"
    }
  ];

  return (
    <section className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Why Fadhili?
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We're reimagining financial cooperation for the digital age, 
            putting power back into the hands of communities.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
            >
              <Card className="h-full border-none shadow-xl hover:shadow-2xl transition-all duration-300 group overflow-hidden">
                <div className="relative h-48 overflow-hidden">
                  <img 
                    src={feature.image}
                    alt={feature.imageAlt}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div>
                  <div className={`absolute top-4 right-4 w-16 h-16 bg-gradient-to-r ${feature.gradient} rounded-2xl flex items-center justify-center shadow-lg`}>
                    <feature.icon className="w-8 h-8 text-white" />
                  </div>
                </div>
                
                <CardContent className="p-8 text-center space-y-4">
                  <h3 className="text-2xl font-bold text-gray-900">
                    {feature.title}
                  </h3>
                  
                  <p className="text-lg text-gray-600 leading-relaxed">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}