import React from "react";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";

export default function CommunitySection() {
  return (
    <section className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center space-y-8"
        >
          <div className="space-y-6">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900">
              Built in Kenya.
              <br />
              <span className="bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                Built for All.
              </span>
            </h2>
            
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              From Nairobi estates to rural cooperatives, we're inspired by how real people 
              move money, build trust, and help each other grow.
            </p>
          </div>

          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="flex flex-wrap justify-center items-center gap-6"
          >
            <Badge variant="outline" className="px-6 py-3 text-lg font-semibold border-2 border-green-200 text-green-700 bg-green-50">
              Early Access Community
            </Badge>
            <Badge variant="outline" className="px-6 py-3 text-lg font-semibold border-2 border-blue-200 text-blue-700 bg-blue-50">
              2025 Launch
            </Badge>
          </motion.div>

          {/* Community Grid */}
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-16"
          >
            {[
              { icon: "🏠", label: "Estate Groups", count: "250+" },
              { icon: "💼", label: "SACCOs", count: "89+" },
              { icon: "👥", label: "Chamas", count: "420+" },
              { icon: "🎓", label: "Student Groups", count: "156+" }
            ].map((item, index) => (
              <motion.div
                key={item.label}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center group"
              >
                <div className="text-4xl md:text-5xl mb-4 group-hover:scale-110 transition-transform duration-300">
                  {item.icon}
                </div>
                <div className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
                  {item.count}
                </div>
                <div className="text-gray-600 font-medium">
                  {item.label}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}