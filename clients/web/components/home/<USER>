import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Quote } from "lucide-react";
import { motion } from "framer-motion";

export default function TestimonialSection() {
  return (
    <section className="py-24 bg-white">
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <Card className="border-none shadow-2xl bg-gradient-to-br from-blue-50 to-indigo-50">
            <CardContent className="p-12 md:p-16">
              <div className="space-y-8">
                <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto">
                  <Quote className="w-8 h-8 text-white" />
                </div>
                
                <blockquote className="text-2xl md:text-3xl font-bold text-gray-900 leading-relaxed">
                  "The power is shifting to us. With Fadhili, we run our chama like a real institution."
                </blockquote>
                
                <div className="space-y-2">
                  <div className="w-16 h-16 bg-gray-300 rounded-full mx-auto mb-4"></div>
                  <p className="text-lg font-semibold text-gray-900">Early User</p>
                  <p className="text-gray-600">Nairobi</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  );
}