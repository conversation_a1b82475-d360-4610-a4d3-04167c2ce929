import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ArrowRight, Smartphone, Users, BarChart3, Vote, DollarSign } from "lucide-react";
import { motion } from "framer-motion";

export default function HeroSection() {
  const [email, setEmail] = useState("");

  const handleEarlyAccess = (e: { preventDefault: () => void; }) => {
    e.preventDefault();
    // Handle early access signup
    console.log("Early access signup:", email);
    setEmail("");
  };

  return (
    <section className="relative min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-100 overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
        <div className="absolute top-40 right-10 w-96 h-96 bg-indigo-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000"></div>
        <div className="absolute -bottom-20 left-1/2 w-80 h-80 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-25 animate-pulse delay-2000"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
        <div className="grid lg:grid-cols-2 gap-12 items-center min-h-[90vh]">
          {/* Left Content */}
          <motion.div 
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="space-y-8"
          >
            <div className="space-y-6">
              <motion.h1 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="text-5xl md:text-6xl lg:text-7xl font-bold text-gray-900 leading-tight"
              >
                Communities
                <br />
                <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                  Deserve More
                </span>
                <br />
                Than Banks.
              </motion.h1>
              
              <motion.p 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="text-xl md:text-2xl text-gray-600 leading-relaxed max-w-2xl"
              >
                We're building Africa's first community-driven banking platform — 
                <span className="font-semibold text-gray-800"> decentralized, transparent, and trusted</span> by 
                those who matter most: you.
              </motion.p>
            </div>

            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="space-y-4"
            >
              <form onSubmit={handleEarlyAccess} className="flex flex-col sm:flex-row gap-4 max-w-lg">
                <Input
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="flex-1 h-14 text-lg px-6 border-2 border-gray-200 focus:border-blue-500 rounded-xl"
                  required
                />
                <Button 
                  type="submit"
                  className="h-14 px-8 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 group"
                >
                  Get Early Access
                  <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </Button>
              </form>
              
              <p className="text-sm text-gray-500">
                <span className="inline-flex items-center gap-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></span>
                  Coming Soon – Join the waitlist
                </span>
              </p>
            </motion.div>
          </motion.div>

          {/* Right Content - Computer Mockup */}
          <div className="relative flex justify-center items-center h-full">
            <div className="relative w-full max-w-2xl">
              {/* Laptop Screen */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 1, delay: 0.8 }}
                className="relative z-10"
              >
                <div className="aspect-[4/3] rounded-2xl bg-white p-2 md:p-3 shadow-2xl border-4 border-gray-800">
                  <div className="w-full h-full bg-slate-100 rounded-xl p-3 md:p-6 space-y-4 overflow-y-auto">
                    <div className="flex items-center justify-between">
                      <h3 className="text-xl font-bold text-gray-900">Community Dashboard</h3>
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-green-600">Live</span>
                        <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                      </div>
                    </div>
                    
                    <div className="bg-white rounded-lg p-4 shadow">
                      <p className="text-base font-semibold text-gray-800 mb-2">Active Proposal: New Equipment</p>
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-gray-500">Vote Progress</span>
                        <span className="font-semibold text-blue-600">12 / 15 Votes (80%)</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <div className="bg-blue-600 h-2 rounded-full" style={{width: '80%'}}></div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-white p-4 rounded-lg shadow text-center">
                        <div className="text-2xl font-bold text-gray-900">KES 342,500</div>
                        <div className="text-sm text-gray-600">Total Funds</div>
                      </div>
                      <div className="bg-white p-4 rounded-lg shadow text-center">
                        <div className="text-2xl font-bold text-green-600">1,247</div>
                        <div className="text-sm text-gray-600">Active Members</div>
                      </div>
                    </div>
                  </div>
                </div>
                {/* Laptop Base */}
                <div className="w-full h-4 bg-gray-300 rounded-b-xl mx-auto -mt-1 relative z-0">
                  <div className="w-24 h-1 bg-gray-400/50 mx-auto mt-2 rounded-full"></div>
                </div>
              </motion.div>

              {/* Floating Elements */}
              <motion.div 
                drag
                dragConstraints={{ top: -100, left: -150, right: 150, bottom: 100 }}
                initial={{ opacity: 0, y: 50, scale: 0.8 }}
                animate={{ 
                  opacity: 1, 
                  scale: 1,
                  y: [0, -20, 0],
                }}
                transition={{ 
                  opacity: { delay: 1.2, duration: 0.8 },
                  scale: { delay: 1.2, duration: 0.8 },
                  y: { repeat: Infinity, duration: 5, ease: "easeInOut" }
                }}
                className="absolute z-20 -top-12 -right-12 bg-white/50 backdrop-blur-lg rounded-2xl p-4 shadow-xl border border-white/30 cursor-grab active:cursor-grabbing"
              >
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                    <BarChart3 className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">+12.5%</p>
                    <p className="text-sm text-gray-600">Growth</p>
                  </div>
                </div>
              </motion.div>

              <motion.div 
                drag
                dragConstraints={{ top: -100, left: -150, right: 150, bottom: 100 }}
                initial={{ opacity: 0, y: -50, scale: 0.8 }}
                animate={{ 
                  opacity: 1, 
                  scale: 1,
                  y: [0, 20, 0],
                }}
                transition={{ 
                  opacity: { delay: 1.4, duration: 0.8 },
                  scale: { delay: 1.4, duration: 0.8 },
                  y: { repeat: Infinity, duration: 6, ease: "easeInOut", delay: 0.5 }
                }}
                className="absolute z-20 -bottom-12 -left-12 bg-white/50 backdrop-blur-lg rounded-2xl p-4 shadow-xl border border-white/30 cursor-grab active:cursor-grabbing"
              >
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <Users className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">KES 2.4M</p>
                    <p className="text-sm text-gray-600">Shared</p>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
