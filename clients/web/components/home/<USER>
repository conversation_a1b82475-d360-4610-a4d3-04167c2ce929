import React from "react";
import { motion } from "framer-motion";

export default function PartnersSection() {
  const partners = [
    { name: "Sequo<PERSON>", logo: "🏛️" },
    { name: "a16z", logo: "🚀" },
    { name: "Coinbase Ventures", logo: "🪙" },
    { name: "Binance Labs", logo: "🌟" },
    { name: "Polygon", logo: "💎" },
    { name: "Chainlink", logo: "🔗" }
  ];

  return (
    <section className="py-24 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center space-y-12"
        >
          <div>
            <p className="text-lg text-gray-600 mb-8 font-medium">
              Backed by the world's most renowned investors
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 items-center">
            {partners.map((partner, index) => (
              <motion.div
                key={partner.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex flex-col items-center space-y-3 group cursor-pointer"
              >
                <div className="text-4xl group-hover:scale-110 transition-transform duration-300">
                  {partner.logo}
                </div>
                <div className="text-gray-700 font-semibold text-sm">
                  {partner.name}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}