import { useEffect, useState } from "react";
import logoUrl from "../assets/Fadh (1).svg";
import darkLogoUrl from "../assets/Fadh (2).svg";


export const Logo = () => {
    const [isDark, setIsDark] = useState(false);

    useEffect(() => {
        // Check if dark mode is already set
        const isDarkMode = document.documentElement.classList.contains('dark');
        setIsDark(isDarkMode);

        // Optional: Listen for changes to the dark mode class
        const observer = new MutationObserver(() => {
            const updatedIsDark = document.documentElement.classList.contains('dark');
            setIsDark(updatedIsDark);
        });

        observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] });

        return () => observer.disconnect();
    }, []);

    return (
        isDark ? (
            <div className="w-28">
                <img src={darkLogoUrl} alt="logo" className="w-full"/>
            </div>
        ) : (
            <div className="w-28">
                <img src={logoUrl} alt="logo" className="w-full"/>
            </div>
        )
    );
}