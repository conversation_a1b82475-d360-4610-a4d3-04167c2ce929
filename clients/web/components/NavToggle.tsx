import { MenuIcon, X } from "lucide-react";
import { useEffect, useState } from "react";
import { But<PERSON> } from "./ui/button";
import { Drawer, DrawerContent, DrawerTrigger } from "./ui/drawer";
import { Link } from "./Link";

export  function NavMobileToggle () {
    const [isNavOpen, setIsNavOpen] = useState(false);

    const toggleNav = () => {
        setIsNavOpen(!isNavOpen);
    };

    useEffect(() => {
        if (isNavOpen) {
            document.body.classList.add('overflow-hidden');
        } else {
            document.body.classList.remove('overflow-hidden');
        }
    }, [isNavOpen]);
    return (
        <div>
            {/*  */}
            <Drawer>
                <DrawerTrigger asChild>
                    <Button type="button" onClick={toggleNav} aria-expanded={isNavOpen} aria-label="Open navigation menu" className="flex justify-center items-center">
                        {isNavOpen ? <X/> : <MenuIcon/>}
                    </Button>
                </DrawerTrigger>
                <DrawerContent>
                    <div className="flex flex-col gap-4 p-4">
                        <Link href="/">Home</Link>
                        <Link href="/about">About</Link>
                        <Link href="/contact">Contact</Link>
                        <Link href="/blog">Blog</Link>
                    </div>
                </DrawerContent>
                
            </Drawer>
        </div>
        )

}