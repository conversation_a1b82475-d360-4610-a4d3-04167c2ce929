import { MenuIcon, X } from "lucide-react";
import { useEffect, useState } from "react";
import { Button } from "./ui/button";
import { Drawer, DrawerClose, DrawerContent, DrawerTrigger } from "./ui/drawer";
import { Link } from "./Link";

export  function NavMobileToggle () {
    const [isNavOpen, setIsNavOpen] = useState(false);

    const toggleNav = () => {
        setIsNavOpen(!isNavOpen);
    };

    useEffect(() => {
        if (isNavOpen) {
            document.body.classList.add('overflow-hidden');
        } else {
            document.body.classList.remove('overflow-hidden');
        }
    }, [isNavOpen]);
    return (
        <div>
            {/*  */}
            <Drawer open={isNavOpen} onClose={toggleNav} onOpenChange={toggleNav}direction="top">
                <DrawerTrigger asChild>
                    <Button type="button" onClick={toggleNav} aria-expanded={isNavOpen} aria-label="Open navigation menu" className="flex justify-center items-center px-8 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 group">
                        {isNavOpen ? <X/> : <MenuIcon/>}
                    </Button>
                </DrawerTrigger>
                <DrawerContent className=" bg-white h-64  w-screen max-w-none max-h-none rounded-none border-none">
                    <div className="flex flex-col gap-4 p-4">
                        <div className="flex justify-end
                         items-center">
                            
                            <DrawerClose asChild >
                                <Button className="rounded-[50%] w-10 h-10 px-8 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 group" onClick={toggleNav}>
                                    <X/>
                                </Button>
                            </DrawerClose>
                        </div>
                        <Link href="/">Home</Link>
                        <Link href="/about">About</Link>
                        <Link href="/contact">Contact</Link>
                        <Link href="/blog">Blog</Link>
                    </div>
                </DrawerContent>
                
            </Drawer>
        </div>
        )

}