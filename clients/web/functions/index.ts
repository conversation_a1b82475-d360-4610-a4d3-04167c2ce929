import * as functions from "firebase-functions";
import express from "express";
import path from "path";

// Dynamically import the Vike SSR build output
const { renderPage } = await import("../../clients/web/dist/server/entry.mjs");

const app = express();

app.use(express.static(path.resolve(__dirname, "../../clients/web/dist/client")));

app.get("*", async (req, res) => {
  const pageContextInit = { urlOriginal: req.originalUrl };
  const pageContext = await renderPage(pageContextInit);
  const { httpResponse } = pageContext;

  if (!httpResponse) {
    res.status(404).send("Not Found");
    return;
  }

  const { body, statusCode, contentType } = httpResponse;
  res.status(statusCode).type(contentType).send(body);
});

export const ssr = functions.https.onRequest(app);
