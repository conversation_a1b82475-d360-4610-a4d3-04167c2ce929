// src/pages/+Page.tsx

export default function HomePage() {
  return (
    <main className="space-y-8">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-foreground mb-4">
          Welcome to Fadhili
        </h1>
        <p className="text-lg text-muted-foreground">
          Your custom theme is now active! Try the theme toggle in the header.
        </p>
      </div>

      {/* Theme Color Showcase */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <h3 className="text-lg font-semibold text-card-foreground mb-3">Primary Colors</h3>
          <div className="space-y-2">
            <div className="bg-primary text-primary-foreground px-3 py-2 rounded">Primary</div>
            <div className="bg-secondary text-secondary-foreground px-3 py-2 rounded">Secondary</div>
            <div className="bg-accent text-accent-foreground px-3 py-2 rounded">Accent</div>
          </div>
        </div>

        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <h3 className="text-lg font-semibold text-card-foreground mb-3">Your Fadhili Colors</h3>
          <div className="grid grid-cols-3 gap-2">
            <div className="bg-fadhili-800 h-12 rounded flex items-center justify-center text-white text-xs">#20212F</div>
            <div className="bg-fadhili-700 h-12 rounded flex items-center justify-center text-white text-xs">#0047A4</div>
            <div className="bg-fadhili-600 h-12 rounded flex items-center justify-center text-white text-xs">#1280BB</div>
            <div className="bg-fadhili-500 h-12 rounded flex items-center justify-center text-white text-xs">#0096C7</div>
            <div className="bg-fadhili-400 h-12 rounded flex items-center justify-center text-white text-xs">#00B4D8</div>
            <div className="bg-fadhili-300 h-12 rounded flex items-center justify-center text-fadhili-800 text-xs">#48CAE4</div>
            <div className="bg-fadhili-200 h-12 rounded flex items-center justify-center text-fadhili-800 text-xs">#90E0EF</div>
            <div className="bg-fadhili-100 h-12 rounded flex items-center justify-center text-fadhili-800 text-xs">#ADE8F4</div>
            <div className="bg-fadhili-50 h-12 rounded flex items-center justify-center text-fadhili-800 text-xs">#CAF0F8</div>
          </div>
        </div>

        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <h3 className="text-lg font-semibold text-card-foreground mb-3">Interactive Elements</h3>
          <div className="space-y-3">
            <button type="button" className="w-full bg-primary text-primary-foreground px-4 py-2 rounded hover:opacity-90 transition-opacity">
              Primary Button
            </button>
            <button type="button" className="w-full bg-secondary text-secondary-foreground px-4 py-2 rounded hover:opacity-90 transition-opacity">
              Secondary Button
            </button>
            <input
              type="text"
              placeholder="Input field"
              className="w-full bg-input border border-border px-3 py-2 rounded focus:ring-2 focus:ring-ring focus:outline-none"
            />
          </div>
        </div>
      </div>

      <div className="bg-muted rounded-lg p-6">
        <h2 className="text-2xl font-semibold text-foreground mb-4">Theme Features</h2>
        <ul className="space-y-2 text-muted-foreground">
          <li>✅ Custom Fadhili color palette with 9 shades</li>
          <li>✅ Automatic dark mode with inverted colors</li>
          <li>✅ Semantic color tokens (primary, secondary, accent, etc.)</li>
          <li>✅ Theme toggle with localStorage persistence</li>
          <li>✅ System preference detection</li>
          <li>✅ Tailwind CSS integration</li>
        </ul>
      </div>
    </main>
  );
}

