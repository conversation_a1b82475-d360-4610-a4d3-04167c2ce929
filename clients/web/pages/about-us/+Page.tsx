import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { 
  ArrowRight, 
  Mail, 
  Phone, 
  MapPin, 
  Linkedin, 
  Twitter,
  Users,
  Target,
  Heart,
  Globe,
  Building2,
  Handshake
} from "lucide-react";
import { motion } from "framer-motion";

// Import team images
import teamCEO from "@/assets/alikula.webp";
import teamCTO from "@/assets/seb.webp";
import teamProduct from "@/assets/martin.webp";
import teamDesign from "@/assets/aluda.webp";
import teamCollaboration from "@/assets/teamwork.webp";
import partnershipHero from "@/assets/business.webp";

export default function About() {
  const [email, setEmail] = useState("");
  const [message, setMessage] = useState("");

  const handlePartnershipSubmit = (e: { preventDefault: () => void; }) => {
    e.preventDefault();
    console.log("Partnership inquiry:", { email, message });
    setEmail("");
    setMessage("");
  };

  const teamMembers = [
    {
      name: "<PERSON><PERSON><PERSON> Ali<PERSON>la",
      role: "Founder, Chief-Exec",
      bio: "Seasoned developer, Passionate about problem-solving and delivering impactful software solutions Exporing new concept and deas",
      image: teamCEO,
      linkedin: "https://www.linkedin.com/in/caldewood-alikula-06701715b/",
      twitter: "https://x.com/alikula0"
    },
    {
      name: "Sebastian Kimanzi",
      role: "Chief-Tech", 
      bio: "I am passionate and committed to staying at the forefront of emerging technologies and industry best practices, constantly expanding my skill set to adapt to the ever-evolving software landscape. I strive to be a versatile software engineer.",
      image: teamCTO,
      linkedin: "https://www.linkedin.com/in/sebastian-k-j/",
      twitter: "#"
    },
    {
      name: "Martin Maina",
      role: "Chief-Ops",
      bio: "Full Stack Developer with a passion for building scalable digital solutions that streamline operations and drive business growth. With over 4 years of hands-on experience across software development",
      image: teamProduct,
      linkedin: "https://www.linkedin.com/in/martin-githae-11781b20a/",
      twitter: "https://x.com/MartinBilson?t=Q8paa_dGWVkX8Stn1Sl5lQ&s=08"
    },
    {
      name: "Aluda Amoshe",
      role: "Chief-Legal",
      bio: "Advocate of the High Court of Kenya, legal consultant, and legalpreneur. I bring a wealth of experience in corporate and commercial law, regulatory compliance, dispute resolution, legal advisory and other fields of law",
      image: teamDesign,
      linkedin: "https://www.linkedin.com/in/aluda-amoshe-7850b1226/",
      twitter: "#"
    }
  ];



  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative min-h-[80vh] bg-gradient-to-br from-blue-50 via-white to-blue-100 overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
          <div className="absolute top-40 right-10 w-96 h-96 bg-indigo-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
          <div className="grid lg:grid-cols-2 gap-12 items-center min-h-[70vh]">
            {/* Left Content */}
            <motion.div 
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
              className="space-y-8"
            >
              <div className="space-y-6">
                <motion.h1 
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                  className="text-5xl md:text-6xl font-bold text-gray-900 leading-tight"
                >
                  Building
                  <br />
                  <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                    Africa's Future
                  </span>
                  <br />
                  Together.
                </motion.h1>
                
                <motion.p 
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                  className="text-xl md:text-2xl text-gray-600 leading-relaxed"
                >
                  We're a team of passionate innovators, community builders, and 
                  financial technology experts committed to democratizing access to 
                  financial services across Africa.
                </motion.p>
              </div>
            </motion.div>

            {/* Right Content - Team Image */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 1, delay: 0.6 }}
              className="relative"
            >
              <div className="relative rounded-3xl overflow-hidden shadow-2xl">
                <img 
                  src={teamCollaboration}
                  alt="Fadhili team collaborating"
                  className="w-full h-[500px] object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                
                {/* Floating stat cards */}
                <motion.div 
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.2, duration: 0.8 }}
                  className="absolute bottom-6 left-6 bg-white/90 backdrop-blur-lg rounded-2xl p-4 shadow-xl"
                >
                  <div className="text-center">
                    <p className="text-2xl font-bold text-gray-900">50K+</p>
                    <p className="text-sm text-gray-600">Communities Served</p>
                  </div>
                </motion.div>
                
                <motion.div 
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.4, duration: 0.8 }}
                  className="absolute top-6 right-6 bg-white/90 backdrop-blur-lg rounded-2xl p-4 shadow-xl"
                >
                  <div className="text-center">
                    <p className="text-2xl font-bold text-blue-600">$10M+</p>
                    <p className="text-sm text-gray-600">Funds Managed</p>
                  </div>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Our Values Section */}
      {/* <section className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Our Values
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              The principles that guide everything we do and every decision we make.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full border-none shadow-lg hover:shadow-xl transition-all duration-300 group">
                  <CardContent className="p-8 text-center space-y-4">
                    <div className={`w-16 h-16 mx-auto bg-gradient-to-r ${value.gradient} rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                      <value.icon className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900">
                      {value.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      {value.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section> */}

      {/* Team Section */}
      <section className="py-24 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Meet Our Team
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Experienced leaders from top financial institutions and tech companies, 
              united by a shared vision of financial inclusion.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {teamMembers.map((member, index) => (
              <motion.div
                key={member.name}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full border-none shadow-xl hover:shadow-2xl transition-all duration-300 group overflow-hidden">
                  <div className="relative overflow-hidden">
                    <img 
                      src={member.image}
                      alt={member.name}
                      className="w-full h-80 object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    
                    {/* Social Links */}
                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <a 
                        href={member.linkedin}
                        className="w-10 h-10 bg-white/90 rounded-full flex items-center justify-center shadow-lg hover:bg-blue-600 hover:text-white transition-colors"
                      >
                        <Linkedin className="w-5 h-5" />
                      </a>
                      <a 
                        href={member.twitter}
                        className="w-10 h-10 bg-white/90 rounded-full flex items-center justify-center shadow-lg hover:bg-blue-400 hover:text-white transition-colors"
                      >
                        <Twitter className="w-5 h-5" />
                      </a>
                    </div>
                  </div>
                  
                  <CardContent className="p-6 space-y-3">
                    <h3 className="text-xl font-bold text-gray-900">
                      {member.name}
                    </h3>
                    <p className="text-blue-600 font-semibold">
                      {member.role}
                    </p>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {member.bio}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Partnership Section */}
      <section className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left Content */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="space-y-8"
            >
              <div className="space-y-6">
                <h2 className="text-4xl md:text-5xl font-bold text-gray-900 leading-tight">
                  Let's Build
                  <br />
                  <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                    Something Great
                  </span>
                  <br />
                  Together.
                </h2>
                
                <p className="text-xl text-gray-600 leading-relaxed">
                  We're always looking for passionate partners who share our vision 
                  of transforming financial services in Africa. Whether you're an 
                  investor, NGO, government entity, or technology partner, let's explore 
                  how we can collaborate.
                </p>
              </div>

              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <Building2 className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Strategic Partnerships</h3>
                    <p className="text-gray-600">Long-term collaborations that create lasting impact</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                    <Handshake className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Investment Opportunities</h3>
                    <p className="text-gray-600">Be part of Africa's financial revolution</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                    <Globe className="w-6 h-6 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Technology Integration</h3>
                    <p className="text-gray-600">Integrate with our platform and APIs</p>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Right Content - Partnership Form */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <Card className="p-8 shadow-2xl border-none bg-gradient-to-br from-white to-blue-50">
                <CardContent className="p-0 space-y-6">
                  <div className="relative h-48 -mx-8 -mt-8 mb-8 overflow-hidden rounded-t-lg">
                    <img 
                      src={partnershipHero}
                      alt="Partnership collaboration"
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div>
                    <div className="absolute bottom-4 left-6">
                      <h3 className="text-2xl font-bold text-white">Partnership Inquiry</h3>
                      <p className="text-white/90">Let's start the conversation</p>
                    </div>
                  </div>

                  <form onSubmit={handlePartnershipSubmit} className="space-y-6">
                    <div className="grid grid-cols-2 gap-4">
                      <Input
                        type="text"
                        placeholder="First Name"
                        className="h-12 border-2 border-gray-200 focus:border-blue-500 rounded-xl"
                        required
                      />
                      <Input
                        type="text"
                        placeholder="Last Name"
                        className="h-12 border-2 border-gray-200 focus:border-blue-500 rounded-xl"
                        required
                      />
                    </div>
                    
                    <Input
                      type="email"
                      placeholder="Email Address"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="h-12 border-2 border-gray-200 focus:border-blue-500 rounded-xl"
                      required
                    />
                    
                    <Input
                      type="text"
                      placeholder="Company/Organization"
                      className="h-12 border-2 border-gray-200 focus:border-blue-500 rounded-xl"
                      required
                    />
                    
                    <textarea
                      placeholder="Tell us about your partnership idea..."
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      className="w-full h-32 p-4 border-2 border-gray-200 focus:border-blue-500 rounded-xl resize-none focus:outline-none"
                      required
                    />
                    
                    <Button 
                      type="submit"
                      className="w-full h-14 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 group"
                    >
                      Start Partnership Discussion
                      <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

     
    </div>
  );
}