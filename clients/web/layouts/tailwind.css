@import "tailwindcss";
@import "tw-animate-css";
@custom-variant dark (&:is(.dark *));

:root {
  --radius: 0.625rem;

  /* Your custom Fadhili theme - Light Mode */
  --background: #fff; /* Custom Scrollbar Styles */

  --scrollbar-bg: #e0f7fa;
  --scrollbar-thumb: #0096c7;
  --scrollbar-thumb-hover: #48cae4;
  --scrollbar-track: #f1f1f1;
  --scrollbar-radius: 8px;

  /* Chrome, Edge, Safari */
  *::-webkit-scrollbar {
    width: 12px;
    height: 12px;
    background: var(--scrollbar-track);
    border-radius: var(--scrollbar-radius);
  }
  *::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb);
    border-radius: var(--scrollbar-radius);
  }
  *::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-hover);
  }
  *::-webkit-scrollbar-corner {
    background: var(--scrollbar-bg);
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
  }
  --foreground: #20212f;
  --card: #ffffff;
  --card-foreground: #20212f;
  --popover: #ffffff;
  --popover-foreground: #20212f;
  --primary: #0096c7;
  --primary-foreground: #ffffff;
  --secondary: #48cae4;
  --secondary-foreground: #20212f;
  --muted: #90e0ef;
  --muted-foreground: #20212f;
  --accent: #00b4d8;
  --accent-foreground: #ffffff;
  --destructive: #dc2626;
  --destructive-foreground: #ffffff;
  --border: #90e0ef;
  --input: #ade8f4;
  --ring: #0096c7;

  /* Chart colors using your theme */
  --chart-1: #0096c7;
  --chart-2: #48cae4;
  --sidebar: #ffffff;
  --scrollbar-bg: #e0f7fa; 
  --scrollbar-thumb: #0096c7; 
  --scrollbar-thumb-hover: #48cae4; 
  --scrollbar-track: #f1f1f1; 
  --scrollbar-radius: 8px; 
  --sidebar-foreground: #20212f;
  --sidebar-primary: #0096c7;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #48cae4;
  --sidebar-accent-foreground: #20212f;
  --sidebar-border: #90e0ef;
  --sidebar-ring: #0096c7;
}

.dark {
  /* Your custom Fadhili theme - Dark Mode */
  --background: #20212f;
  --foreground: #caf0f8;
  --card: #1a1b26;
  --card-foreground: #caf0f8;
  --popover: #1a1b26;
  --popover-foreground: #caf0f8;
  --primary: #48cae4;
  --primary-foreground: #20212f;
  --secondary: #1280bb;
  --secondary-foreground: #caf0f8;
  --muted: #0047a4;
  --muted-foreground: #ade8f4;
  --accent: #90e0ef;
  --accent-foreground: #20212f;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #1280bb;
  --input: #0047a4;
  --ring: #48cae4;

  /* Chart colors for dark mode */
  --chart-1: #48cae4;
  --chart-2: #90e0ef;
  --chart-3: #ade8f4;
  --chart-4: #00b4d8;
  --chart-5: #0096c7;

  /* Sidebar colors for dark mode */
  --sidebar: #1a1b26;
  --sidebar-foreground: #caf0f8;
  --sidebar-primary: #48cae4;
  --sidebar-primary-foreground: #20212f;
  --sidebar-accent: #1280bb;
  --sidebar-accent-foreground: #caf0f8;
  --sidebar-border: #0047a4;
  --sidebar-ring: #48cae4;
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
