@import "tailwindcss";
@import "tw-animate-css";
@custom-variant dark (&:is(.dark *));

:root {
  --radius: 0.625rem;

  /* Your custom Fadhili theme - Light Mode */
  --background: #CAF0F8;
  --foreground: #20212F;
  --card: #ffffff;
  --card-foreground: #20212F;
  --popover: #ffffff;
  --popover-foreground: #20212F;
  --primary: #0096C7;
  --primary-foreground: #ffffff;
  --secondary: #48CAE4;
  --secondary-foreground: #20212F;
  --muted: #90E0EF;
  --muted-foreground: #20212F;
  --accent: #00B4D8;
  --accent-foreground: #ffffff;
  --destructive: #dc2626;
  --destructive-foreground: #ffffff;
  --border: #90E0EF;
  --input: #ADE8F4;
  --ring: #0096C7;

  /* Chart colors using your theme */
  --chart-1: #0096C7;
  --chart-2: #48CAE4;
  --chart-3: #00B4D8;
  --chart-4: #1280BB;
  --chart-5: #0047A4;

  /* Sidebar colors */
  --sidebar: #ffffff;
  --sidebar-foreground: #20212F;
  --sidebar-primary: #0096C7;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #48CAE4;
  --sidebar-accent-foreground: #20212F;
  --sidebar-border: #90E0EF;
  --sidebar-ring: #0096C7;
}

.dark {
  /* Your custom Fadhili theme - Dark Mode */
  --background: #20212F;
  --foreground: #CAF0F8;
  --card: #1a1b26;
  --card-foreground: #CAF0F8;
  --popover: #1a1b26;
  --popover-foreground: #CAF0F8;
  --primary: #48CAE4;
  --primary-foreground: #20212F;
  --secondary: #1280BB;
  --secondary-foreground: #CAF0F8;
  --muted: #0047A4;
  --muted-foreground: #ADE8F4;
  --accent: #90E0EF;
  --accent-foreground: #20212F;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #1280BB;
  --input: #0047A4;
  --ring: #48CAE4;

  /* Chart colors for dark mode */
  --chart-1: #48CAE4;
  --chart-2: #90E0EF;
  --chart-3: #ADE8F4;
  --chart-4: #00B4D8;
  --chart-5: #0096C7;

  /* Sidebar colors for dark mode */
  --sidebar: #1a1b26;
  --sidebar-foreground: #CAF0F8;
  --sidebar-primary: #48CAE4;
  --sidebar-primary-foreground: #20212F;
  --sidebar-accent: #1280BB;
  --sidebar-accent-foreground: #CAF0F8;
  --sidebar-border: #0047A4;
  --sidebar-ring: #48CAE4;
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
