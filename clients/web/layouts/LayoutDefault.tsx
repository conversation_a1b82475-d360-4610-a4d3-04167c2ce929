import { ReactNode } from "react";
import { Link } from "../components/Link";
import { ThemeToggle } from "../components/ThemeToggle";
import "./tailwind.css";
import { Logo } from "@/components/Logo";
import { NavMobileToggle } from "@/components/NavToggle";

export default function Layout({ children }: { children: ReactNode }) {
  return (
    <div className="min-h-screen w-screen bg-background flex flex-col text-foreground">
      <Header />
      <main className="flex flex-1 max-w-6xl mx-auto bg-background w-full px-4 py-6 mt-16">
        <div className="w-full">{children}</div>
      </main>
      <Footer />
    </div>
  );
}

function Header() {
  return (
    <header className=" w-full h-16 mx-auto fixed flex justify-center items-center bg-transparent  ">
      <nav className=" mx-auto flex items-center justify-between w-[96vw] h-14  z-1 border-[#0047a4]/25 border rounded-lg fixed bg-transparent backdrop-blur-md shadow-md p-2">
        {/* logo */}
        <div>
          <Link href="/">
            <Logo />
          </Link>
        </div>

        {/* page nav items /menu*/}
        <div className="hidden items-center gap-4 md:flex">
          {/* <ThemeToggle /> */}
          <Link href="/">Home</Link>
          <Link href="/about">About</Link>
          <Link href="/contact">Contact</Link>
          <Link href="/blog">Blog</Link>
          <button>
            Get started
          </button>
        </div>
        <div className="md:hidden flex justify-center items-center gap-4">
          {/* mobile toggle */}
          <NavMobileToggle />
        </div>
      </nav>
    </header>
  );
}

function Footer() {
  return (
    <footer className="border-t border-border bg-card mt-auto">
      <div className="max-w-6xl mx-auto px-4 py-6 text-sm text-center text-muted-foreground">
        © {new Date().getFullYear()} Fadhili. Built with ❤️ in Kenya.
      </div>
    </footer>
  );
}
