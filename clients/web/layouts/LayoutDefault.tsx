import { ReactNode } from "react";
import { Link } from "../components/Link";
import "./tailwind.css";
import { Logo } from "@/components/Logo";
import { NavMobileToggle } from "@/components/NavToggle";
import { Button } from "@/components/ui/button";
import { Heart } from "lucide-react";


export default function Layout({ children }: { children: ReactNode }) {
  return (
    <div className="min-h-screen  bg-background flex flex-col text-foreground">
      <Header />
      <main className="flex flex-1  mx-auto  w-full px-4 py-6 mt-16">
        <div className="w-full">{children}</div>
      </main>
      <Footer />
    </div>
  );
}

function Header() {
  return (
    <header className=" w-full h-16 mx-auto flex justify-center items-center bg-transparent fixed z-10 ">
      <nav className=" mx-auto flex items-center justify-between w-[96vw] h-14  z-10 border-[#0047a4]/25 border rounded-lg fixed bg-transparent backdrop-blur-md shadow-md p-2">
        {/* logo */}
        <div>
          <Link href="/">
            <Logo />
          </Link>
        </div>

        {/* page nav items /menu*/}
        <div className="hidden items-center gap-4 lg:flex mr-6">
          {/* <ThemeToggle /> */}
          <Link href="/">Home</Link>
           <Link href="/about-us">Us</Link>
          {/* <ThemeToggle/> */}
        
        </div>
        <div className="lg:hidden flex justify-center items-center gap-4 ">
          {/* mobile toggle */}
          <NavMobileToggle />
        </div>
      </nav>
    </header>
  );
}


 function Footer() {
  return (
    <footer className="bg-gray-900 text-white py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid md:grid-cols-4 gap-8 mb-12">
          {/* Brand */}
          <div className="space-y-4">
            <h3 className="text-2xl font-bold">Fadhili</h3>
            <p className="text-gray-400 leading-relaxed">
              Africa's first community-driven banking platform, built for trust and transparency.
            </p>
          </div>

          {/* Navigation */}
          <div className="space-y-4">
            <h4 className="font-semibold text-lg">Navigation</h4>
            <ul className="space-y-2 text-gray-400">
              <li className="hover:text-white transition-colors"><Link href="/" >Home</Link></li>
              <li className="hover:text-white transition-colors"><Link href="/about-us">About Us</Link></li>
            </ul>
          </div>

          {/* Social */}
          <div className="space-y-4">
            <h4 className="font-semibold text-lg">Community</h4>
            <ul className="space-y-2 text-gray-400">
              <li><a href="#" className="hover:text-white transition-colors">Twitter</a></li>
              <li><a href="#" className="hover:text-white transition-colors">Telegram</a></li>
              <li><a href="#" className="hover:text-white transition-colors">GitHub</a></li>
            </ul>
          </div>

          {/* Legal */}
          <div className="space-y-4">
            <h4 className="font-semibold text-lg">Legal</h4>
            <ul className="space-y-2 text-gray-400">
              <li><a href="#" className="hover:text-white transition-colors">Privacy</a></li>
              <li><a href="#" className="hover:text-white transition-colors">Terms</a></li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-gray-400 text-sm">
              © 2025 Fadhili Labs. All rights reserved.
            </p>
            <p className="text-gray-400 text-sm flex items-center gap-2">
              Made with <Heart className="w-4 h-4 text-red-500" /> in Africa.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}