import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './layouts/**/*.{js,ts,jsx,tsx,mdx}',
    './lib/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        // Your custom theme colors
        fadhili: {
          // Light theme (your provided colors)
          50: '#CAF0F8',   // lightest
          100: '#ADE8F4',  
          200: '#90E0EF',  
          300: '#48CAE4',  
          400: '#00B4D8',  
          500: '#0096C7',  // primary
          600: '#1280BB',  
          700: '#0047A4',  // darker
          800: '#20212F',  // darkest
          900: '#1a1b26',  // even darker for dark mode
        },
        // Semantic color mappings
        primary: {
          DEFAULT: '#0096C7',
          foreground: '#ffffff',
        },
        secondary: {
          DEFAULT: '#48CAE4',
          foreground: '#20212F',
        },
        background: {
          DEFAULT: '#CAF0F8',
          dark: '#20212F',
        },
        foreground: {
          DEFAULT: '#20212F',
          dark: '#CAF0F8',
        },
        muted: {
          DEFAULT: '#90E0EF',
          foreground: '#20212F',
          dark: '#1a1b26',
          'dark-foreground': '#ADE8F4',
        },
        accent: {
          DEFAULT: '#00B4D8',
          foreground: '#ffffff',
          dark: '#48CAE4',
          'dark-foreground': '#20212F',
        },
        border: {
          DEFAULT: '#90E0EF',
          dark: '#1280BB',
        },
        input: {
          DEFAULT: '#ADE8F4',
          dark: '#1a1b26',
        },
        ring: {
          DEFAULT: '#0096C7',
          dark: '#48CAE4',
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
    },
  },
  plugins: [],
  darkMode: 'class',
}

export default config
