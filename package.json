{"name": "fadhili-monorepo", "version": "0.1.0", "private": true, "workspaces": ["clients/*", "contracts/*"], "engines": {"node": "20.11.1"}, "scripts": {"build:web": "cd clients/web && npm run build", "build:functions": "cd functions && npm run build", "build": "npm run build:web && npm run build:functions", "deploy:web": "firebase deploy --only hosting:web", "deploy:functions": "firebase deploy --only functions", "deploy": "firebase deploy", "serve": "firebase emulators:start"}, "dependencies": {"firebase-tools": "^14.11.1"}}