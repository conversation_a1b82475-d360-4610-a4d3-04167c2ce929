{"name": "functions", "version": "1.0.0", "description": "Firebase Functions for Fadhili Web App", "main": "lib/index.js", "engines": {"node": ">=20"}, "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "dependencies": {"firebase-admin": "^12.0.0", "firebase-functions": "^6.4.0", "express": "^5.1.0"}, "devDependencies": {"typescript": "^5.8.3", "@types/express": "^4.17.21"}, "private": true}