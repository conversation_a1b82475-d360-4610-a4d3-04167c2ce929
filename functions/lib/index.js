import * as functions from "firebase-functions";
import express from "express";
import path from "path";
const app = express();
// Serve static files from the client build
app.use(express.static(path.resolve(__dirname, "../../clients/web/dist/client")));
app.get("*", async (req, res) => {
    try {
        // Dynamically import the Vike SSR build output
        const { renderPage } = await import("../../clients/web/dist/server/entry.mjs");
        const pageContextInit = { urlOriginal: req.originalUrl };
        const pageContext = await renderPage(pageContextInit);
        const { httpResponse } = pageContext;
        if (!httpResponse) {
            res.status(404).send("Not Found");
            return;
        }
        const { body, statusCode, contentType } = httpResponse;
        res.status(statusCode).type(contentType).send(body);
    }
    catch (error) {
        console.error("SSR Error:", error);
        res.status(500).send("Internal Server Error");
    }
});
export const ssr = functions.https.onRequest(app);
//# sourceMappingURL=index.js.map