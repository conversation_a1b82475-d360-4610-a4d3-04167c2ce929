{"compilerOptions": {"module": "ESNext", "target": "ES2022", "lib": ["ES2022"], "declaration": false, "strict": true, "noImplicitReturns": true, "noImplicitThis": true, "noImplicitAny": false, "strictNullChecks": true, "noUnusedLocals": false, "noUnusedParameters": false, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true, "sourceMap": true, "rootDir": "src", "outDir": "lib", "resolveJsonModule": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true}, "include": ["src/**/*"], "exclude": ["node_modules", "lib"]}